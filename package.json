{"name": "blackwoods-creative-website", "version": "1.1.0", "description": "Premium portfolio website for BlackWoods Creative - showcasing filmmaking, photography, 3D, and scene creation", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:strict": "npx eslint src --ext .ts,.tsx --max-warnings 0", "lint:fix": "npx eslint src --ext .ts,.tsx --fix", "lint:check": "npx eslint src --ext .ts,.tsx --max-warnings 50", "lint:report": "npx eslint src --ext .ts,.tsx --format html --output-file eslint-report.html", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true next build", "analyze:report": "node scripts/bundle-analysis.js", "analyze:full": "npm run analyze && npm run analyze:report", "bundle:check": "npm run build && npm run analyze:report", "bundle:optimize": "npm run analyze:full && echo 'Check .next/analyze/bundle-report.json for optimization recommendations'", "test": "jest", "test:watch": "jest --watch", "e2e": "playwright test", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@heroicons/react": "^2.2.0", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.35.0", "clsx": "^2.0.0", "framer-motion": "^10.16.0", "next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "resend": "^4.6.0", "sharp": "^0.32.0", "tailwind-merge": "^2.0.0", "typescript": "^5.0.0"}, "devDependencies": {"@axe-core/react": "^4.10.2", "@next/bundle-analyzer": "^14.0.0", "@playwright/test": "^1.52.0", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "autoprefixer": "^10.4.0", "axe-core": "^4.10.3", "cross-env": "^7.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-promise": "^6.6.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^1.0.4", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "playwright": "^1.40.0", "postcss": "^8.4.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "tailwindcss": "^3.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["portfolio", "creative-agency", "filmmaking", "photography", "3d-visualization", "next.js", "typescript", "framer-motion"], "author": "BlackWoods Creative", "license": "UNLICENSED"}