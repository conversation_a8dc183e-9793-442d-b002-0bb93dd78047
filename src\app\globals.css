@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties - BlackWoods Creative Deep Forest Haze Theme */
:root {
  /* Core Deep Forest Haze Colors */
  --bw-bg-primary: #101211; /* Near Black with organic green tint */
  --bw-text-primary: #E8E8E3; /* Off-White, warm and soft on eyes */
  --bw-accent-gold: #C3A358; /* Muted Gold, rich ochre for CTAs */

  /* Aurora Background Colors - ENHANCED VISIBILITY */
  --bw-aurora-teal: #0F3530; /* Enhanced Dark Teal - more visible */
  --bw-aurora-green: #1E4A38; /* Enhanced Forest Green - more visible */
  --bw-aurora-bright: #2E6B5E; /* Enhanced Brighter accent - more visible */

  /* Utility Colors */
  --bw-border-subtle: #2A2E2C; /* Low-contrast borders and dividers */

  /* Legacy colors for gradual migration */
  --bw-black: #101211; /* Updated to match bg-primary */
  --bw-charcoal: #0f0f0f;
  --bw-dark-gray: #1a1a1a;
  --bw-medium-gray: #2a2a2a;
  --bw-light-gray: #6a6a6a;
  --bw-white: #E8E8E3; /* Updated to match text-primary */
  --bw-silver: #b8b8b8;
  --bw-platinum: #f0f0f0;
  --bw-gold: #C3A358; /* Fixed to match accent-gold */
  --bw-red: #cc3333;

  /* Enhanced atmospheric colors */
  --bw-pearl: #f8f8f8;
  --bw-smoke: #8a8a8a;
  --bw-obsidian: #0a0a0a;
  --bw-champagne: #f7e7ce;

  /* Deep Forest Haze Gradients - ENHANCED FOREST GREEN PROMINENCE */
  --bw-gradient-aurora:
    radial-gradient(circle at 20% 30%, var(--bw-aurora-green) 0%, transparent 65%),
    radial-gradient(circle at 80% 70%, var(--bw-aurora-green) 0%, transparent 60%),
    radial-gradient(circle at 40% 80%, var(--bw-aurora-teal) 0%, transparent 55%),
    radial-gradient(circle at 60% 20%, var(--bw-aurora-green) 0%, transparent 50%),
    radial-gradient(circle at 30% 60%, var(--bw-aurora-bright) 0%, transparent 45%);

  --bw-gradient-primary: linear-gradient(135deg, var(--bw-bg-primary) 0%, #0a0f0b 50%, var(--bw-bg-primary) 100%);
  --bw-gradient-gold: linear-gradient(135deg, var(--bw-accent-gold) 0%, #d4b85a 50%, var(--bw-accent-gold) 100%);
  --bw-gradient-glass: linear-gradient(135deg, rgba(16, 18, 17, 0.5) 0%, rgba(16, 18, 17, 0.3) 100%);

  /* Legacy gradients for migration */
  --bw-gradient-dark: linear-gradient(135deg, var(--bw-bg-primary) 0%, #0f0f0f 50%, #1a1a1a 100%);
  --bw-gradient-silver: linear-gradient(135deg, #b8b8b8 0%, #f0f0f0 100%);
  --bw-gradient-glow: radial-gradient(circle, rgba(232, 232, 227, 0.15) 0%, transparent 70%);
  --bw-gradient-obsidian: linear-gradient(135deg, var(--bw-bg-primary) 0%, #0a0a0a 100%);
  --bw-gradient-pearl: linear-gradient(135deg, #f8f8f8 0%, var(--bw-text-primary) 100%);
  --bw-gradient-smoke: linear-gradient(135deg, #6a6a6a 0%, #8a8a8a 100%);
  --bw-gradient-depth: linear-gradient(180deg, transparent 0%, rgba(16,18,17,0.8) 100%);

  /* Enhanced Shadows - Sophisticated Depth System */
  --shadow-cinematic: 0 25px 50px -12px rgba(0, 0, 0, 0.9);
  --shadow-glow: 0 0 20px rgba(255, 255, 255, 0.15);
  --shadow-gold-glow: 0 0 30px rgba(212, 175, 55, 0.4);

  /* New Sophisticated Shadow Layers */
  --shadow-obsidian: 0 8px 32px rgba(0, 0, 0, 0.6);
  --shadow-pearl: 0 4px 20px rgba(255, 255, 255, 0.1);
  --shadow-depth-1: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-depth-2: 0 8px 24px rgba(0, 0, 0, 0.4);
  --shadow-depth-3: 0 16px 48px rgba(0, 0, 0, 0.5);
  --shadow-depth-4: 0 32px 64px rgba(0, 0, 0, 0.6);
  --shadow-inset: inset 0 2px 4px rgba(0, 0, 0, 0.3);

  /* Deep Forest Haze Spacing System - 8px Grid */
  --space-xs: 4px;   /* $space-xs from theme guide */
  --space-sm: 8px;   /* $space-sm from theme guide */
  --space-md: 16px;  /* $space-md from theme guide */
  --space-lg: 32px;  /* $space-lg from theme guide */
  --space-xl: 64px;  /* $space-xl from theme guide */
  --space-xxl: 128px; /* $space-xxl from theme guide */

  /* Section Spacing - Generous White Space */
  --section-padding-sm: var(--space-4xl); /* 96px */
  --section-padding-md: var(--space-5xl); /* 128px */
  --section-padding-lg: var(--space-6xl); /* 192px */

  /* Animation Timing - Refined for Tasteful Interactions */
  --ease-cinematic: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-dramatic: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-gentle: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-smooth: cubic-bezier(0.25, 0.1, 0.25, 1);
  --duration-fast: 300ms;
  --duration-normal: 500ms;
  --duration-slow: 700ms;
  --duration-cinematic: 900ms;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  background: var(--bw-bg-primary);
  position: relative;
}

/* Deep Forest Haze Aurora Background - ENHANCED VISIBILITY */
body::before {
  content: '';
  position: fixed;
  top: -30%;
  left: -30%;
  right: -30%;
  bottom: -30%;
  background: var(--bw-gradient-aurora);
  filter: blur(60px);
  animation: auroraFlow 60s ease-in-out infinite alternate;
  z-index: -2;
  opacity: 1.0; /* Enhanced visibility */
}

/* Secondary aurora layer for depth - ENHANCED FOREST GREEN BLEEDING */
body::after {
  content: '';
  position: fixed;
  top: -20%;
  left: -20%;
  right: -20%;
  bottom: -20%;
  background:
    radial-gradient(circle at 60% 20%, var(--bw-aurora-green) 0%, transparent 45%),
    radial-gradient(circle at 30% 90%, var(--bw-aurora-green) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, var(--bw-aurora-green) 0%, transparent 40%),
    radial-gradient(circle at 15% 50%, var(--bw-aurora-teal) 0%, transparent 35%),
    radial-gradient(circle at 85% 40%, var(--bw-aurora-bright) 0%, transparent 30%);
  filter: blur(40px);
  animation: auroraFlow 45s ease-in-out infinite alternate-reverse;
  z-index: -1;
  opacity: 0.85; /* Enhanced visibility */
}

/* Smooth Scrolling */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bw-charcoal);
}

::-webkit-scrollbar-thumb {
  background: var(--bw-medium-gray);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--bw-light-gray);
}

/* Selection Styles */
::selection {
  background: var(--bw-gold);
  color: var(--bw-black);
}

/* Focus Styles */
:focus-visible {
  outline: 2px solid var(--bw-gold);
  outline-offset: 2px;
}

/* Utility Classes */
@layer utilities {
  .text-gradient-gold {
    background: var(--bw-gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .bg-gradient-dark {
    background: var(--bw-gradient-dark);
  }

  .shadow-cinematic {
    box-shadow: var(--shadow-cinematic);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .shadow-gold-glow {
    box-shadow: var(--shadow-gold-glow);
  }

  .shadow-cinematic-lg {
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  /* Enhanced Shadow System */
  .shadow-obsidian {
    box-shadow: var(--shadow-obsidian);
  }

  .shadow-pearl {
    box-shadow: var(--shadow-pearl);
  }

  .shadow-depth-1 {
    box-shadow: var(--shadow-depth-1);
  }

  .shadow-depth-2 {
    box-shadow: var(--shadow-depth-2);
  }

  .shadow-depth-3 {
    box-shadow: var(--shadow-depth-3);
  }

  .shadow-depth-4 {
    box-shadow: var(--shadow-depth-4);
  }

  .shadow-inset {
    box-shadow: var(--shadow-inset);
  }

  /* Enhanced Background Gradients */
  .bg-gradient-obsidian {
    background: var(--bw-gradient-obsidian);
  }

  .bg-gradient-pearl {
    background: var(--bw-gradient-pearl);
  }

  .bg-gradient-smoke {
    background: var(--bw-gradient-smoke);
  }

  .bg-gradient-depth {
    background: var(--bw-gradient-depth);
  }

  .transition-cinematic {
    transition: all var(--duration-cinematic) var(--ease-cinematic);
  }

  .transition-dramatic {
    transition: all var(--duration-slow) var(--ease-dramatic);
  }
}

/* Deep Forest Haze Component Styles */
@layer components {
  .btn-primary {
    background: var(--bw-accent-gold);
    color: var(--bw-bg-primary);
    padding: var(--space-md) var(--space-lg); /* 16px 32px per theme guide */
    border-radius: 8px;
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    transition: all 0.3s ease-out;
    border: none;
    cursor: pointer;
    min-height: 44px; /* Accessibility: Minimum touch target size */
    min-width: 44px;
  }

  .btn-primary:hover {
    background: #d4b85a; /* 10% brighter per theme guide */
    transform: translateY(-1px); /* more subtle lift */
    box-shadow: 0 4px 15px rgba(195, 163, 88, 0.2);
    transition: all var(--duration-slow) var(--ease-gentle);
  }

  .btn-secondary {
    background: transparent;
    color: var(--bw-accent-gold);
    border: 2px solid var(--bw-accent-gold);
    padding: var(--space-md) var(--space-lg); /* 16px 32px per theme guide */
    border-radius: 8px;
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    transition: all var(--duration-slow) var(--ease-gentle);
    cursor: pointer;
    min-height: 44px; /* Accessibility: Minimum touch target size */
    min-width: 44px;
  }

  .btn-secondary:hover {
    background: var(--bw-accent-gold); /* fills with accent-gold per theme guide */
    color: var(--bw-bg-primary); /* text becomes bg-primary per theme guide */
  }

  .card {
    background:
      linear-gradient(135deg, rgba(16, 18, 17, 0.5) 0%, rgba(26, 66, 48, 0.1) 100%),
      rgba(16, 18, 17, 0.5); /* Forest green bleeding effect */
    backdrop-filter: blur(12px); /* frosted glass effect per theme guide */
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--bw-border-subtle);
    border-radius: 16px;
    padding: var(--space-lg); /* 32px per theme guide */
    transition: all var(--duration-slow) var(--ease-gentle);
  }

  .card:hover {
    border-color: var(--bw-accent-gold); /* border animates to accent-gold per theme guide */
    transform: translateY(-1px); /* more subtle lift */
    transition: all var(--duration-normal) var(--ease-gentle);
  }

  .card-elevated {
    background:
      linear-gradient(135deg, rgba(16, 18, 17, 0.7) 0%, rgba(30, 74, 56, 0.15) 100%),
      rgba(16, 18, 17, 0.7); /* Enhanced forest green bleeding */
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(195, 163, 88, 0.2);
    border-radius: 16px;
    padding: 32px;
    transition: all var(--duration-slow) var(--ease-gentle);
  }

  .card-elevated:hover {
    border-color: var(--bw-accent-gold);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(195, 163, 88, 0.15);
    transition: all var(--duration-normal) var(--ease-gentle);
  }

  .card-premium {
    background:
      linear-gradient(135deg, rgba(16, 18, 17, 0.8) 0%, rgba(46, 107, 94, 0.2) 100%),
      rgba(16, 18, 17, 0.8); /* Premium forest green bleeding */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 2px solid rgba(195, 163, 88, 0.3);
    border-radius: 16px;
    padding: 32px;
    transition: all var(--duration-slow) var(--ease-gentle);
  }

  .card-premium:hover {
    border-color: var(--bw-accent-gold);
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(195, 163, 88, 0.25);
    transition: all var(--duration-normal) var(--ease-gentle);
  }

  .input-field {
    background: rgba(16, 18, 17, 0.3);
    border: 1px solid var(--bw-border-subtle);
    border-radius: 8px;
    padding: 12px 16px;
    color: var(--bw-text-primary);
    font-family: 'Inter', sans-serif;
    transition: all var(--duration-slow) var(--ease-gentle);
  }

  .input-field::placeholder {
    color: var(--bw-text-primary);
    opacity: 0.6;
  }

  .input-field:focus {
    outline: none;
    border-color: var(--bw-accent-gold);
    box-shadow: 0 0 20px rgba(195, 163, 88, 0.3);
  }

  /* Deep Forest Haze Typography System - EXACT THEME GUIDE IMPLEMENTATION */

  /* h1 (Main Headline) - Theme Guide Specification + Responsive */
  .text-display-xl {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem; /* 40px mobile */
    font-weight: 600; /* Semi-bold per theme guide */
    letter-spacing: -0.02em; /* Per theme guide */
    color: var(--bw-accent-gold); /* $accent-gold per theme guide */
    line-height: 1.1;
  }

  @media (min-width: 768px) {
    .text-display-xl {
      font-size: 3rem; /* 48px tablet */
    }
  }

  @media (min-width: 1024px) {
    .text-display-xl {
      font-size: 4rem; /* 64px desktop per theme guide */
    }
  }

  /* h2 (Section Headings) - Theme Guide Specification + Responsive */
  .text-display-lg {
    font-family: 'Playfair Display', serif;
    font-size: 1.875rem; /* 30px mobile */
    font-weight: 600; /* Per theme guide */
    letter-spacing: -0.01em; /* Per theme guide */
    color: var(--bw-text-primary); /* $text-primary per theme guide */
    line-height: 1.2;
  }

  @media (min-width: 768px) {
    .text-display-lg {
      font-size: 2.25rem; /* 36px tablet */
    }
  }

  @media (min-width: 1024px) {
    .text-display-lg {
      font-size: 2.5rem; /* 40px desktop per theme guide */
    }
  }

  /* h3 (Card Titles, Sub-headings) - Theme Guide Specification */
  .text-display-md {
    font-family: 'Inter', sans-serif;
    font-size: 1.5rem; /* 24px per theme guide */
    font-weight: 600; /* Per theme guide */
    color: var(--bw-text-primary); /* $text-primary per theme guide */
    line-height: 1.3;
  }

  /* p (Body Text) - Theme Guide Specification */
  .text-body-xl {
    font-family: 'Inter', sans-serif;
    font-size: 1rem; /* 16px per theme guide */
    font-weight: 400; /* Regular per theme guide */
    line-height: 1.6; /* Per theme guide */
    color: var(--bw-text-primary); /* $text-primary per theme guide */
    opacity: 0.85; /* 85% opacity per theme guide */
  }

  /* small (Captions, Meta info) - Theme Guide Specification */
  .text-body-lg {
    font-family: 'Inter', sans-serif;
    font-size: 0.875rem; /* 14px per theme guide */
    font-weight: 400; /* Per theme guide */
    color: var(--bw-text-primary); /* $text-primary per theme guide */
    opacity: 0.6; /* 60% opacity per theme guide */
  }

  /* Enhanced Visual Effects */
  .backdrop-blur-cinematic {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .transition-bounce {
    transition: all var(--duration-normal) var(--ease-gentle);
  }

  /* Theme Guide Scroll Animation - Intersection Observer */
  .scroll-fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease-out, transform 0.6s ease-out;
  }

  .scroll-fade-in.visible {
    opacity: 1;
    transform: translateY(0);
  }

  /* Deep Forest Haze Focus States for Accessibility - ENHANCED */
  /* Only show focus for keyboard navigation, not mouse clicks */
  button:focus-visible,
  a:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible,
  [tabindex]:focus-visible {
    outline: 3px solid var(--bw-accent-gold);
    outline-offset: 2px;
    box-shadow: 0 0 0 1px var(--bw-bg-primary), 0 0 8px rgba(195, 163, 88, 0.6);
  }

  /* Skip Links for Accessibility */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--bw-accent-gold);
    color: var(--bw-bg-primary);
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 600;
    z-index: 1000;
    transition: top 0.3s ease;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* Remove default focus for mouse users */
  *:focus:not(:focus-visible) {
    outline: none;
    box-shadow: none;
  }
}

/* Deep Forest Haze Animation Keyframes */
@keyframes auroraFlow {
  0% {
    transform: rotate(0deg) translateX(0px) translateY(0px) scale(1);
  }
  25% {
    transform: rotate(2deg) translateX(15px) translateY(-10px) scale(1.02);
  }
  50% {
    transform: rotate(4deg) translateX(25px) translateY(5px) scale(1.04);
  }
  75% {
    transform: rotate(6deg) translateX(10px) translateY(15px) scale(1.02);
  }
  100% {
    transform: rotate(8deg) translateX(30px) translateY(-5px) scale(1.06);
  }
}

@keyframes particleDrift {
  0% {
    transform: translateY(0px) translateX(0px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(50px);
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseGlow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(195, 163, 88, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(195, 163, 88, 0.6);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Performance Optimizations - Enhanced for Phase 4 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Animation Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-scroll {
  will-change: scroll-position;
}

.will-change-auto {
  will-change: auto;
}

/* Parallax Performance */
.parallax-element {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Animation Performance Classes */
.animate-optimized {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Remove will-change after animation */
.animate-complete {
  will-change: auto;
}

/* Accessibility Enhancements - Phase 4 */
/* Focus indicators handled by :focus-visible pseudo-class above */

/* Enhanced Reduced Motion Support for Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Disable specific animations */
  .parallax-element {
    transform: none !important;
  }

  .animate-optimized {
    animation: none !important;
    transform: none !important;
  }

  /* Disable floating animations */
  .animate-float {
    animation: none !important;
  }

  /* Disable pulse animations */
  .animate-pulse-slow {
    animation: none !important;
  }

  /* Disable aurora background animation */
  body::before {
    animation: none !important;
  }

  /* Disable particle animations */
  .particle {
    animation: none !important;
    opacity: 0.3 !important;
  }

  /* Disable magnetic field effects */
  [data-magnetic] {
    transform: none !important;
  }

  /* Disable hover scale effects */
  *:hover {
    transform: none !important;
    scale: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --bw-text-primary: #ffffff;
    --bw-text-secondary: #e0e0e0;
    --bw-bg-primary: #000000;
    --bw-border-subtle: #ffffff;
    --bw-accent-gold: #ffff00;
  }
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--bw-accent-gold);
  color: var(--bw-bg-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* Duplicate removed - consolidated above */
