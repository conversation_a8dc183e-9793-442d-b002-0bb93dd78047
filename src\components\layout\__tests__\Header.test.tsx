import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { ThemeProvider } from '@/context/ThemeContext';
import { scrollToElement } from '@/lib/utils';

import { Header } from '../Header';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    header: ({ children, ...props }: React.ComponentProps<'header'>) => <header {...props}>{children}</header>,
    div: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: React.ComponentProps<'button'>) => <button {...props}>{children}</button>,
    span: ({ children, ...props }: React.ComponentProps<'span'>) => <span {...props}>{children}</span>,
  },
  AnimatePresence: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

// Mock the scroll utility
jest.mock('@/lib/utils', () => ({
  ...jest.requireActual('@/lib/utils'),
  scrollToElement: jest.fn(),
  throttle: jest.fn((fn) => fn), // Return the function as-is for testing
  cn: jest.fn((...classes) => classes.filter(Boolean).join(' ')),
}));

// Mock siteConfig
jest.mock('@/lib/constants/siteConfig', () => ({
  siteConfig: {
    name: 'BlackWoods Creative',
    navigation: [
      { name: 'Portfolio', href: '#portfolio' },
      { name: 'About', href: '#about' },
      { name: 'Contact', href: '#contact' },
    ],
  },
}));
const mockScrollToElement = scrollToElement as jest.MockedFunction<typeof scrollToElement>;

// Helper function to render Header with ThemeProvider
const renderHeader = (props = {}) => {
  return render(
    <ThemeProvider>
      <Header {...props} />
    </ThemeProvider>
  );
};

describe('Header', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset scroll position
    Object.defineProperty(window, 'scrollY', { value: 0, writable: true });

    // Mock DOM methods
    Object.defineProperty(window, 'addEventListener', {
      value: jest.fn(),
      writable: true,
    });
    Object.defineProperty(window, 'removeEventListener', {
      value: jest.fn(),
      writable: true,
    });

    // Mock document.body.style
    Object.defineProperty(document.body, 'style', {
      value: { overflow: 'unset' },
      writable: true,
    });
  });

  it('renders header with brand text and navigation', () => {
    renderHeader();

    expect(screen.getByText('BlackWoods Creative')).toBeInTheDocument();
    expect(screen.getByText('Portfolio')).toBeInTheDocument();
    expect(screen.getByText('About')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
  });

  it('renders mobile menu button', () => {
    renderHeader();

    const mobileMenuButton = screen.getByLabelText('Open mobile menu');
    expect(mobileMenuButton).toBeInTheDocument();
  });

  it('toggles mobile menu when button is clicked', async () => {
    const user = userEvent.setup();
    renderHeader();

    const mobileMenuButton = screen.getByLabelText('Open mobile menu');

    // Menu should be closed initially - check for navigation items
    const portfolioLinks = screen.getAllByText('Portfolio');
    expect(portfolioLinks.length).toBeGreaterThan(0);

    // Open menu
    await user.click(mobileMenuButton);

    // Should have both desktop and mobile navigation items visible
    const portfolioLinksAfterOpen = screen.getAllByText('Portfolio');
    expect(portfolioLinksAfterOpen.length).toBeGreaterThanOrEqual(1);

    // Close menu
    await user.click(mobileMenuButton);

    // Navigation should still be present
    const portfolioLinksAfterClose = screen.getAllByText('Portfolio');
    expect(portfolioLinksAfterClose.length).toBeGreaterThan(0);
  });

  it('closes mobile menu when navigation link is clicked', async () => {
    const user = userEvent.setup();
    renderHeader();

    const mobileMenuButton = screen.getByLabelText('Open mobile menu');

    // Open menu
    await user.click(mobileMenuButton);

    // Click a navigation link (get the first one)
    const portfolioLinks = screen.getAllByText('Portfolio');
    await user.click(portfolioLinks[0]);

    // Verify scrollToElement was called
    expect(mockScrollToElement).toHaveBeenCalledWith('#portfolio', 80);
  });

  it('calls scrollToElement when navigation links are clicked', async () => {
    const user = userEvent.setup();
    renderHeader();

    const portfolioLinks = screen.getAllByText('Portfolio');
    await user.click(portfolioLinks[0]);

    expect(mockScrollToElement).toHaveBeenCalledWith('#portfolio', 80);
  });

  it('handles scroll events and updates header background', () => {
    renderHeader();

    // Verify the scroll event handler is attached
    expect(window.addEventListener).toHaveBeenCalledWith('scroll', expect.any(Function), { passive: true });
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    renderHeader();

    const portfolioLinks = screen.getAllByText('Portfolio');
    portfolioLinks[0].focus();

    await user.keyboard('{Enter}');
    expect(mockScrollToElement).toHaveBeenCalledWith('#portfolio', 80);
  });

  it('applies custom className', () => {
    renderHeader({ className: "custom-header" });
    const header = screen.getByRole('banner');
    expect(header).toHaveClass('custom-header');
  });

  it('renders all navigation items', () => {
    renderHeader();

    const expectedNavItems = ['Portfolio', 'About', 'Contact'];

    expectedNavItems.forEach(item => {
      expect(screen.getByText(item)).toBeInTheDocument();
    });
  });

  it('handles escape key to close mobile menu', async () => {
    const user = userEvent.setup();
    renderHeader();

    const mobileMenuButton = screen.getByLabelText('Open mobile menu');

    // Open menu
    await user.click(mobileMenuButton);

    // Press escape - this would close the menu in real implementation
    await user.keyboard('{Escape}');

    // For now, just verify the menu button is still there
    expect(mobileMenuButton).toBeInTheDocument();
  });

  it('maintains accessibility attributes', () => {
    renderHeader();

    const mobileMenuButton = screen.getByLabelText('Open mobile menu');
    expect(mobileMenuButton).toBeInTheDocument();

    // The navigation should be present
    const navigation = screen.getByRole('navigation');
    expect(navigation).toBeInTheDocument();
  });

  it('handles multiple rapid clicks on mobile menu button', async () => {
    const user = userEvent.setup();
    renderHeader();

    const mobileMenuButton = screen.getByLabelText('Open mobile menu');

    // Rapidly click multiple times
    await user.click(mobileMenuButton);
    await user.click(mobileMenuButton);
    await user.click(mobileMenuButton);

    // Should handle the state correctly - menu button should still be there
    expect(mobileMenuButton).toBeInTheDocument();
  });

  it('cleans up scroll event listener on unmount', () => {
    const { unmount } = renderHeader();

    unmount();

    expect(window.removeEventListener).toHaveBeenCalledWith('scroll', expect.any(Function));
  });
});
