{"extends": ["next/core-web-vitals", "next/typescript", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:@typescript-eslint/recommended", "plugin:import/recommended", "plugin:import/typescript", "plugin:jsx-a11y/recommended", "prettier"], "plugins": ["react", "react-hooks", "@typescript-eslint", "import", "jsx-a11y"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}, "project": "./tsconfig.json"}, "settings": {"react": {"version": "detect"}, "import/resolver": {"typescript": {"alwaysTryTypes": true, "project": "./tsconfig.json"}}}, "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-non-null-assertion": "warn", "@typescript-eslint/no-var-requires": "error", "@typescript-eslint/ban-ts-comment": "warn", "@typescript-eslint/no-empty-function": "warn", "@typescript-eslint/no-inferrable-types": "error", "@typescript-eslint/prefer-as-const": "error", "@typescript-eslint/prefer-nullish-coalescing": "error", "@typescript-eslint/prefer-optional-chain": "error", "@typescript-eslint/no-unnecessary-type-assertion": "error", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "react/display-name": "warn", "react/no-unescaped-entities": "error", "react/jsx-key": "error", "react/jsx-no-duplicate-props": "error", "react/jsx-no-undef": "error", "react/jsx-uses-react": "off", "react/jsx-uses-vars": "error", "react/no-deprecated": "warn", "react/no-direct-mutation-state": "error", "react/no-find-dom-node": "error", "react/no-is-mounted": "error", "react/no-render-return-value": "error", "react/no-string-refs": "error", "react/no-unknown-property": "error", "react/require-render-return": "error", "react/self-closing-comp": "error", "react/jsx-boolean-value": ["error", "never"], "react/jsx-curly-brace-presence": ["error", {"props": "never", "children": "never"}], "react/jsx-fragments": ["error", "syntax"], "react/jsx-no-useless-fragment": "error", "react/jsx-pascal-case": "error", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}], "import/no-unresolved": "error", "import/no-cycle": "error", "import/no-self-import": "error", "import/no-useless-path-segments": "error", "import/no-duplicates": "error", "import/first": "error", "import/newline-after-import": "error", "import/no-default-export": "off", "no-console": "warn", "no-debugger": "error", "no-alert": "error", "no-var": "error", "prefer-const": "error", "prefer-arrow-callback": "error", "arrow-spacing": "error", "no-duplicate-imports": "error", "no-unused-expressions": "error", "no-unreachable": "error", "no-unreachable-loop": "error", "no-unsafe-finally": "error", "no-unsafe-optional-chaining": "error", "no-useless-backreference": "error", "no-useless-catch": "error", "no-useless-escape": "error", "no-useless-return": "error", "prefer-template": "error", "require-await": "error", "yoda": "error"}, "overrides": [{"files": ["**/__tests__/**/*", "**/*.test.*", "**/*.spec.*"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-non-null-assertion": "off", "react/display-name": "off", "no-console": "off"}}, {"files": ["next.config.js", "jest.config.js", "tailwind.config.js", "postcss.config.js"], "env": {"node": true}, "rules": {"@typescript-eslint/no-var-requires": "off", "import/no-anonymous-default-export": "off"}}, {"files": ["src/app/**/*"], "rules": {"import/no-default-export": "off"}}, {"files": ["src/middleware.ts"], "rules": {"import/no-default-export": "off"}}]}