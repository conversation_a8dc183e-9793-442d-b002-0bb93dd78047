import type { PortfolioProject } from '@/lib/types/portfolio';

// Re-export the type for easier imports
export type { PortfolioProject };

export const portfolioData: PortfolioProject[] = [
  {
    id: 'cinematic-brand-film',
    title: 'Cinematic Brand Film',
    description: 'A compelling brand story that showcases the essence of luxury craftsmanship through cinematic storytelling.',
    category: 'Film',
    type: 'video',
    image: 'https://images.unsplash.com/photo-1485846234645-a62644f84728?w=800&h=600&fit=crop',
    video: '/assets/videos/brand-film.mp4',
    tags: ['Branding', 'Cinematic', 'Luxury', 'Storytelling'],
    client: 'Luxury Brand Co.',
    year: 2024,
    featured: true,
    duration: '2:30',
    software: ['Cinema 4D', 'After Effects', 'Premiere Pro'],
  },
  {
    id: 'architectural-visualization',
    title: 'Modern Architecture Visualization',
    description: 'Photorealistic 3D visualization of a contemporary residential complex showcasing innovative design.',
    category: '3D Visualization',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop',
    tags: ['Architecture', 'Photorealistic', '3D Rendering', 'Design'],
    client: 'Architecture Studio',
    year: 2024,
    featured: true,
    dimensions: '4K Resolution',
    software: ['Blender', 'Unreal Engine', 'Photoshop'],
  },
  {
    id: 'product-photography',
    title: 'Premium Product Photography',
    description: 'High-end product photography series featuring luxury watches with dramatic lighting and composition.',
    category: 'Photography',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800&h=600&fit=crop',
    tags: ['Product', 'Luxury', 'Studio', 'Commercial'],
    client: 'Watch Manufacturer',
    year: 2024,
    dimensions: 'Medium Format',
    software: ['Capture One', 'Photoshop', 'Lightroom'],
  },
  {
    id: 'immersive-scene',
    title: 'Immersive Forest Scene',
    description: 'A mystical forest environment created for virtual reality experience with dynamic lighting and atmosphere.',
    category: 'Scene Creation',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop',
    tags: ['VR', 'Environment', 'Nature', 'Immersive'],
    client: 'VR Experience Co.',
    year: 2024,
    featured: true,
    software: ['Unreal Engine', 'Houdini', 'Substance Painter'],
  },
  {
    id: 'corporate-documentary',
    title: 'Corporate Documentary',
    description: 'Documentary-style film capturing the innovation and culture of a leading technology company.',
    category: 'Film',
    type: 'video',
    image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop',
    video: '/assets/videos/corporate-doc.mp4',
    tags: ['Documentary', 'Corporate', 'Culture', 'Technology'],
    client: 'Tech Innovation Inc.',
    year: 2023,
    duration: '5:45',
    software: ['Premiere Pro', 'DaVinci Resolve', 'After Effects'],
  },
  {
    id: 'fashion-editorial',
    title: 'Fashion Editorial Series',
    description: 'Avant-garde fashion photography series exploring the intersection of technology and haute couture.',
    category: 'Photography',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800&h=600&fit=crop',
    tags: ['Fashion', 'Editorial', 'Avant-garde', 'Technology'],
    client: 'Fashion Magazine',
    year: 2023,
    dimensions: 'Full Frame',
    software: ['Capture One', 'Photoshop'],
  },
  {
    id: 'automotive-visualization',
    title: 'Automotive Visualization',
    description: 'Stunning 3D visualization of a concept car showcasing aerodynamic design and premium materials.',
    category: '3D Visualization',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=800&h=600&fit=crop',
    tags: ['Automotive', 'Concept', 'Design', 'Materials'],
    client: 'Automotive Designer',
    year: 2023,
    featured: true,
    dimensions: '8K Resolution',
    software: ['KeyShot', 'Rhino', 'Photoshop'],
  },
  {
    id: 'urban-environment',
    title: 'Cyberpunk Urban Environment',
    description: 'Futuristic cityscape with neon lighting and atmospheric effects for gaming and entertainment.',
    category: 'Scene Creation',
    type: 'image',
    image: 'https://images.unsplash.com/photo-1519501025264-65ba15a82390?w=800&h=600&fit=crop',
    tags: ['Cyberpunk', 'Urban', 'Futuristic', 'Gaming'],
    client: 'Game Studio',
    year: 2023,
    software: ['Unreal Engine', 'Blender', 'Substance Suite'],
  },
  {
    id: 'music-video',
    title: 'Artistic Music Video',
    description: 'Visually striking music video combining practical effects with digital artistry.',
    category: 'Film',
    type: 'video',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop',
    video: '/assets/videos/music-video.mp4',
    tags: ['Music Video', 'Artistic', 'Effects', 'Creative'],
    client: 'Independent Artist',
    year: 2023,
    duration: '3:45',
    software: ['After Effects', 'Cinema 4D', 'Premiere Pro'],
  },
];
